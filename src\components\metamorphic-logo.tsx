'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface MetamorphicLogoProps {
  className?: string;
  animated?: boolean;
}

export function MetamorphicLogo({ className, animated = false }: MetamorphicLogoProps) {
  return (
    <motion.div
      className={cn('flex items-center bg-transparent', className)}
      style={{ backgroundColor: 'transparent' }}
      initial={animated ? {
        opacity: 0,
        scale: 0.5,
        rotateY: -180,
        y: 50
      } : undefined}
      animate={animated ? {
        opacity: 1,
        scale: 1,
        rotateY: 0,
        y: 0
      } : undefined}
      transition={animated ? {
        duration: 1.2,
        ease: "easeOut",
        type: "spring",
        stiffness: 100,
        damping: 15
      } : undefined}
    >
      <motion.div
        animate={animated ? {
          y: [0, -10, 0],
          rotateZ: [0, 2, -2, 0],
        } : undefined}
        transition={animated ? {
          duration: 4,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1.2
        } : undefined}
      >
        <motion.div
          animate={animated ? {
            scale: [1, 1.05, 1],
            filter: [
              "drop-shadow(0 0 20px rgba(59, 130, 246, 0.5))",
              "drop-shadow(0 0 30px rgba(147, 51, 234, 0.7))",
              "drop-shadow(0 0 20px rgba(59, 130, 246, 0.5))"
            ]
          } : undefined}
          transition={animated ? {
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1.5
          } : undefined}
        >
          <Image
            src="/metamorphic-labs-logo.png"
            alt="Metamorphic Labs"
            width={400}
            height={400}
            className={cn("w-auto bg-transparent", className)}
            style={{ backgroundColor: 'transparent' }}
            priority
          />
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
